[
  {
    "event_id": 9100,
    "Description": "Defeated <PERSON><PERSON> the Grafted"
  },
  {
    "event_id": 9101,
    "Description": "Defeated Margit, the Fell Omen"
  },
  {
    "event_id": 9103,
    "Description": "Defeated Grafted Scion"
  },
  {
    "event_id": 9104,
    "Description": "Defeated Morgo<PERSON>, the Omen King"
  },
  {
    "event_id": 9105,
    "Description": "Defeated Godfrey, First Elden Lord"
  },
  {
    "event_id": 9106,
    "Description": "Defeated Sir Gevent_ideon Ofnir, the All-Knowing"
  },
  {
    "event_id": 9107,
    "Description": "Defeated Hoarah Loux"
  },
  {
    "event_id": 9108,
    "Description": "Defeated As<PERSON>, Naturalborn of the Voevent_id"
  },
  {
    "event_id": 9109,
    "Description": "Defeated Dragonkin Soldier of Nokstella"
  },
  {
    "event_id": 9111,
    "Description": "Defeated Lichdragon Fortissax"
  },
  {
    "event_id": 9112,
    "Description": "Defeated <PERSON>h<PERSON>, Lord of Blood"
  },
  {
    "event_id": 9115,
    "Description": "Defeated Dragonlord Placevent_idusax"
  },
  {
    "event_id": 9116,
    "Description": "Defeated <PERSON>eth, the Black Blade"
  },
  {
    "event_id": 9118,
    "Description": "Defeated Rennala, Queen of the Full Moon"
  },
  {
    "event_id": 9119,
    "Description": "Defeated Royal Knight Loretta"
  },
  {
    "event_id": 9120,
    "Description": "Defeated Malenia, Blade of Miquella"
  },
  {
    "event_id": 9122,
    "Description": "Defeated Rykard, Lord of Blasphemy"
  },
  {
    "event_id": 9123,
    "Description": "Defeated the Elden Beast"
  },
  {
    "event_id": 9130,
    "Description": "Defeated Starscourge Radahn"
  },
  {
    "event_id": 9131,
    "Description": "Defeated Fire Giant"
  },
  {
    "event_id": 9133,
    "Description": "Defeated Regal Ancestor Spirit"
  },
  {
    "event_id": 9134,
    "Description": "Defeated Dragonkin Soldier - Lake of Rot"
  },
  {
    "event_id": 9135,
    "Description": "Defeated Fia's Champion"
  },
  {
    "event_id": 9182,
    "Description": "Defeated Elemer of the Briar"
  },
  {
    "event_id": 9184,
    "Description": "Defeated Commander Niall"
  },
  {
    "event_id": 9143,
    "Description": "Defeated Radahn, Consort of Miquella"
  },
  {
    "event_id": 9146,
    "Description": "Defeated Messmer the Impaler"
  },
  {
    "event_id": 9164,
    "Description": "Defeated Commander Gaius"
  },
  {
    "event_id": 9162,
    "Description": "Defeated Scadutree Avatar"
  },
  {
    "event_id": 9190,
    "Description": "Defeated Rellana, Twin Moon Knight"
  },
  {
    "event_id": 9160,
    "Description": "Defeated Romina, Saint of the Bud"
  },
  {
    "event_id": 9140,
    "Description": "Defeated Divine Beast Dancing Lion"
  },
  {
    "event_id": 9156,
    "Description": "Defeated Mevent_idra, Lord of Frenzied Flame"
  },
  {
    "event_id": 9155,
    "Description": "Defeated Metyr, Mother of Fingers"
  },
  {
    "event_id": 9148,
    "Description": "Defeated Putrescent Knight"
  },
  {
    "event_id": 10000800,
    "Description": "Boss Defeated: Godrick the Grafted"
  },
  {
    "event_id": 10000850,
    "Description": "Boss Defeated: Margit the Fell Omen"
  },
  {
    "event_id": 10010800,
    "Description": "Boss Defeated: Grafted Scion (Chapel of Anticipation)"
  },
  {
    "event_id": 11000800,
    "Description": "Boss Defeated: Morgott, the Omen King"
  },
  {
    "event_id": 11000850,
    "Description": "Boss Defeated: Godfrey, First Elden Lord"
  },
  {
    "event_id": 11050800,
    "Description": "Boss Defeated: Hoarah Loux"
  },
  {
    "event_id": 11050850,
    "Description": "Boss Defeated: Sir Gevent_ideon Ofnir"
  },
  {
    "event_id": 12010800,
    "Description": "Boss Defeated: Dragonkin Soldier of Nokstella"
  },
  {
    "event_id": 12010850,
    "Description": "Boss Defeated: Dragonkin Soldier (Lake of Rot)"
  },
  {
    "event_id": 12020800,
    "Description": "Boss Defeated: Valiant Gargoyles"
  },
  {
    "event_id": 12020830,
    "Description": "Boss Defeated: Dragonkin Soldier of Nokron"
  },
  {
    "event_id": 12020850,
    "Description": "Boss Defeated: Mimic Tear"
  },
  {
    "event_id": 12020390,
    "Description": "Boss Defeated: Crucible Knight"
  },
  {
    "event_id": 12030800,
    "Description": "Boss Defeated: Fia's Champion"
  },
  {
    "event_id": 12030850,
    "Description": "Boss Defeated: Lichdragon Fortissax"
  },
  {
    "event_id": 12040800,
    "Description": "Boss Defeated: Astel, Naturalborn of the Voevent_id"
  },
  {
    "event_id": 12050800,
    "Description": "Boss Defeated: Mohg, Lord of Blood"
  },
  {
    "event_id": 12080800,
    "Description": "Boss Defeated: Ancestor Spirit"
  },
  {
    "event_id": 12090800,
    "Description": "Boss Defeated: Regal Ancestor Spirit"
  },
  {
    "event_id": 13000800,
    "Description": "Boss Defeated: Maliketh, the Black Blade"
  },
  {
    "event_id": 13000830,
    "Description": "Boss Defeated: Dragonlord Placevent_idusax"
  },
  {
    "event_id": 13000850,
    "Description": "Boss Defeated: Godskin Duo"
  },
  {
    "event_id": 14000800,
    "Description": "Boss Defeated: Rennala, Queen of the Full Moon"
  },
  {
    "event_id": 14000850,
    "Description": "Boss Defeated: Red Wolf of Radagon"
  },
  {
    "event_id": 15000800,
    "Description": "Boss Defeated: Malenia, Blade of Miquella"
  },
  {
    "event_id": 15000850,
    "Description": "Boss Defeated: Loretta, Knight of the Haligtree"
  },
  {
    "event_id": 16000800,
    "Description": "Boss Defeated: Rykard, Lord of Blasphemy"
  },
  {
    "event_id": 16000850,
    "Description": "Boss Defeated: Godskin Noble"
  },
  {
    "event_id": 16000860,
    "Description": "Boss Defeated: Abductor Virgins"
  },
  {
    "event_id": 18000800,
    "Description": "Boss Defeated: Ulcerated Tree Spirit"
  },
  {
    "event_id": 18000850,
    "Description": "Boss Defeated: Soldier of Godrick"
  },
  {
    "event_id": 19000800,
    "Description": "Boss Defeated: Elden Beast"
  },
  {
    "event_id": 20000800,
    "Description": "Boss Defeated: Divine Beast Dancing Lion"
  },
  {
    "event_id": 20010800,
    "Description": "Boss Defeated: Promised Consort Radahn"
  },
  {
    "event_id": 21000850,
    "Description": "Boss Defeated: Golden Hippopotamus"
  },
  {
    "event_id": 21010800,
    "Description": "Boss Defeated: Messmer the Impaler"
  },
  {
    "event_id": 22000800,
    "Description": "Boss Defeated: Putrescent Knight"
  },
  {
    "event_id": 25000800,
    "Description": "Boss Defeated: Metyr, Mother of Fingers"
  },
  {
    "event_id": 28000800,
    "Description": "Boss Defeated: Mevent_idra, Lord of Frenzied Flame"
  },
  {
    "event_id": 30000800,
    "Description": "Boss Defeated: Cemetery Shade (Tombsward Catacombs)"
  },
  {
    "event_id": 30010800,
    "Description": "Boss Defeated: Erdtree Burial Watchdog (Impaler's Catacombs)"
  },
  {
    "event_id": 30020800,
    "Description": "Boss Defeated: Erdtree Burial Watchdog (Stormfoot Catacombs)"
  },
  {
    "event_id": 30030800,
    "Description": "Boss Defeated: Spirit-Caller Snail (Road's End Catacombs)"
  },
  {
    "event_id": 30040800,
    "Description": "Boss Defeated: Grave Warden Duelist (Murkwater Catacombs)"
  },
  {
    "event_id": 30050800,
    "Description": "Boss Defeated: Cemetery Shade (Black Knife Catacombs)"
  },
  {
    "event_id": 30050850,
    "Description": "Boss Defeated: Black Knife Assassin (Black Knife Catacombs)"
  },
  {
    "event_id": 30060800,
    "Description": "Boss Defeated: Erdtree Burial Watchdog (Cliffbottom Catacombs)"
  },
  {
    "event_id": 30070800,
    "Description": "Boss Defeated: Erdtree Burial Watchdog (Wyndham Catacombs)"
  },
  {
    "event_id": 30080800,
    "Description": "Boss Defeated: Ancient Hero of Zamor (Sainted Hero's Grave)"
  },
  {
    "event_id": 30090800,
    "Description": "Boss Defeated: Red Wolf of the Champion (Gelmir Hero's Grave)"
  },
  {
    "event_id": 30100800,
    "Description": "Boss Defeated: Crucible Knight Ordovis (Auriza Hero's Grave)"
  },
  {
    "event_id": 30110800,
    "Description": "Boss Defeated: Black Knife Assassin (Deathtouched Catacombs)"
  },
  {
    "event_id": 30120800,
    "Description": "Boss Defeated: Perfumer Tricia (Unsightly Catacombs)"
  },
  {
    "event_id": 30130800,
    "Description": "Boss Defeated: Grave Warden Duelist (Auriza Sevent_ide Tomb)"
  },
  {
    "event_id": 30140800,
    "Description": "Boss Defeated: Erdtree Burial Watchdog (Minor Erdtree Catacombs)"
  },
  {
    "event_id": 30150800,
    "Description": "Boss Defeated: Cemetery Shade (Caelevent_id Catacombs)"
  },
  {
    "event_id": 30160800,
    "Description": "Boss Defeated: Putrevent_id Tree Spirit (War-Dead Catacombs)"
  },
  {
    "event_id": 30170800,
    "Description": "Boss Defeated: Ancient Hero of Zamor (Giant-Conquering Hero's Grave)"
  },
  {
    "event_id": 30180800,
    "Description": "Boss Defeated: Ulcerated Tree Sprit (Giants' Mountaintop Catacombs)"
  },
  {
    "event_id": 30190800,
    "Description": "Boss Defeated: Putrevent_id Grave Warden Duelist (Consecrated Snowfield Catacombs)"
  },
  {
    "event_id": 30200810,
    "Description": "Boss Defeated: Stray Mimic Tear (Hevent_idden Path to the Haligtree)"
  },
  {
    "event_id": 31000800,
    "Description": "Boss Defeated: Patches (Murkwater Cave)"
  },
  {
    "event_id": 31010800,
    "Description": "Boss Defeated: Runebear (Earthbore Cave)"
  },
  {
    "event_id": 31020800,
    "Description": "Boss Defeated: Miranda the Blighted Bloom (Tombsward Cave)"
  },
  {
    "event_id": 31030800,
    "Description": "Boss Defeated: Beastman of Farum Azula (Grovesevent_ide Cave)"
  },
  {
    "event_id": 31040800,
    "Description": "Boss Defeated: Cleanrot Knight (Stillwater Cave)"
  },
  {
    "event_id": 31050800,
    "Description": "Boss Defeated: Bloodhound Knight (Lakesevent_ide Crystal Cave)"
  },
  {
    "event_id": 31060800,
    "Description": "Boss Defeated: Crystalians (Academy Crystal Cave)"
  },
  {
    "event_id": 31070800,
    "Description": "Boss Defeated: Kindred of Rot (Seethewater Cave)"
  },
  {
    "event_id": 31090800,
    "Description": "Boss Defeated: Demi-Human Queen Margot (Volcano Cave)"
  },
  {
    "event_id": 31100800,
    "Description": "Boss Defeated: Beastman of Farum Azula (Dragonbarrow Cave)"
  },
  {
    "event_id": 31110800,
    "Description": "Boss Defeated: Crystalians (Sellia Hevent_ideaway)"
  },
  {
    "event_id": 31120800,
    "Description": "Boss Defeated: Misbegotten Crusader (Cave of the Forlorn)"
  },
  {
    "event_id": 31150800,
    "Description": "Boss Defeated: Demi-Human Chief (Coastal Cave)"
  },
  {
    "event_id": 31170800,
    "Description": "Boss Defeated: Guardian Golem (Highroad Cave)"
  },
  {
    "event_id": 31180800,
    "Description": "Boss Defeated: Omenkiller (Perfumer's Grotto)"
  },
  {
    "event_id": 31190800,
    "Description": "Boss Defeated: Black Knife Assassin (Sage's Cave)"
  },
  {
    "event_id": 31190850,
    "Description": "Boss Defeated: Necromancer Garris (Sage's Cave)"
  },
  {
    "event_id": 31200800,
    "Description": "Boss Defeated: Cleanrot Knight (Abandoned Cave)"
  },
  {
    "event_id": 31210800,
    "Description": "Boss Defeated: Frenzied Duelist (Gaol Cave)"
  },
  {
    "event_id": 31220800,
    "Description": "Boss Defeated: Spirit-Caller Snail (Spiritcaller's Cave)"
  },
  {
    "event_id": 32000800,
    "Description": "Boss Defeated: Scaly Misbegotten (Morne Tunnel)"
  },
  {
    "event_id": 32010800,
    "Description": "Boss Defeated: Stonedigger Troll (Limgrave Tunnels)"
  },
  {
    "event_id": 32020800,
    "Description": "Boss Defeated: Crystalian - Ringblade (Raya Lucaria Crystal Tunnel)"
  },
  {
    "event_id": 32040800,
    "Description": "Boss Defeated: Stonedigger Troll (Old Altus Tunnel)"
  },
  {
    "event_id": 32050800,
    "Description": "Boss Defeated: Crystalian - Ringblade/Spear (Altus Tunnel)"
  },
  {
    "event_id": 32070800,
    "Description": "Boss Defeated: Magma Wyrm (Gael Tunnel)"
  },
  {
    "event_id": 32080800,
    "Description": "Boss Defeated: Fallingstar Beast (Sellia Crystal Tunnel)"
  },
  {
    "event_id": 32110800,
    "Description": "Boss Defeated: Astel, Stars of Darkness"
  },
  {
    "event_id": 34120800,
    "Description": "Boss Defeated: Onyx Lord (Divine Tower of West Altus)"
  }
]



[
  {
    "event_id": 9100,
    "Description": "Defeat the Boss: Cursed Demon Margit"
  },
  {
    "event_id": 9101,
    "Description": "Boss Defeat: Grafting King"
  },
  {
    "event_id": 9102,
    "Description": "Defeat the boss: Edinburgh"
  },
  {
    "event_id": 9103,
    "Description": "Boss Defeat: Grafted Spider"
  },
  {
    "event_id": 9104,
    "Description": "Defeat the boss: Cursed Demon Margit (Serious)"
  },
  {
    "event_id": 9105,
    "Description": "Boss Defeat Godfrey (Godfrey)"
  },
  {
    "event_id": 9106,
    "Description": "Defeat Boss Momochio"
  },
  {
    "event_id": 9107,
    "Description": "Boss Defeat Godfrey"
  },
  {
    "event_id": 9108,
    "Description": "Defeat Boss Aster"
  },
  {
    "event_id": 9109,
    "Description": "Boss Defeat: The Desolate"
  },
  {
    "event_id": 9110,
    "Description": "Defeat the Boss: Gargoyle Tag Team of the Royal Capital"
  },
  {
    "event_id": 9111,
    "Description": "Defeat Boss: Guardian's Descendant (Death)"
  },
  {
    "event_id": 9112,
    "Description": "Boss Defeat Greater Demon"
  },
  {
    "event_id": 9113,
    "Description": "Defeat the Boss: Underground Labyrinth Ark"
  },
  {
    "event_id": 9114,
    "Description": "Boss Defeat God Skin Tag Team"
  },
  {
    "event_id": 9115,
    "Description": "Defeat Boss Ancient Dragon"
  },
  {
    "event_id": 9116,
    "Description": "Defeat Boss Mariquez"
  },
  {
    "event_id": 9117,
    "Description": "Defeat the boss God Skin"
  },
  {
    "event_id": 9118,
    "Description": "Defeat the boss: Lenara"
  },
  {
    "event_id": 9119,
    "Description": "Defeat Boss Tree Guard (Mage)"
  },
  {
    "event_id": 9120,
    "Description": "Defeat Boss Marenia"
  },
  {
    "event_id": 9121,
    "Description": "Defeat the Boss: Volcano Prison Boss"
  },
  {
    "event_id": 9122,
    "Description": "Boss Defeat: King of Ghouls"
  },
  {
    "event_id": 9123,
    "Description": "Defeat the boss Last boss"
  },
  {
    "event_id": 9124,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 9125,
    "Description": "Defeat the boss: Underground Sewer Boss of the Royal Capital"
  },
  {
    "event_id": 9126,
    "Description": "Defeat the boss: Cliff tunnel"
  },
  {
    "event_id": 9127,
    "Description": "Boss Defeat Tutorial"
  },
  {
    "event_id": 9128,
    "Description": "Boss Defeat Tutorial EX"
  },
  {
    "event_id": 9129,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 9130,
    "Description": "Boss Defeat Ladahn"
  },
  {
    "event_id": 9131,
    "Description": "Defeat the Boss Giant"
  },
  {
    "event_id": 9132,
    "Description": "Defeat Boss Ancestral Spirit (Weak)"
  },
  {
    "event_id": 9133,
    "Description": "Defeat Boss Ancestral Spirit (Strong)"
  },
  {
    "event_id": 9134,
    "Description": "Defeat the boss: Transforming Slime"
  },
  {
    "event_id": 9135,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 9140,
    "Description": "Boss Defeat DLC Reservation Slot"
  },
  {
    "event_id": 9170,
    "Description": "Defeat the boss m34_10 Tower of God 1"
  },
  {
    "event_id": 9171,
    "Description": "Defeat the boss m34_11 Tower of God 2"
  },
  {
    "event_id": 9172,
    "Description": "Defeat the boss m34_12 Tower of God 3"
  },
  {
    "event_id": 9173,
    "Description": "Defeat the boss m34_13 Tower of God 4"
  },
  {
    "event_id": 9174,
    "Description": "Defeat the boss m34_14 Tower of God 5"
  },
  {
    "event_id": 9175,
    "Description": "Defeat the boss m34_15 Tower of God 6"
  },
  {
    "event_id": 9180,
    "Description": "Defeat the boss m60_00 Fort (green)"
  },
  {
    "event_id": 9181,
    "Description": "Defeat the boss m60_00 Fort (Lake)"
  },
  {
    "event_id": 9182,
    "Description": "Defeat the boss m60_00 Fort (Takayama)"
  },
  {
    "event_id": 9183,
    "Description": "Defeat the boss m60_00 Fort (plains)"
  },
  {
    "event_id": 9184,
    "Description": "Defeat the boss m60_00 Fort (snow field)"
  },
  {
    "event_id": 9200,
    "Description": "Defeat Boss m30_00 Catacombs 1-1"
  },
  {
    "event_id": 9201,
    "Description": "Defeat Boss m30_01 Catacombs 1-2"
  },
  {
    "event_id": 9202,
    "Description": "Defeat Boss m30_02 Catacombs 1-3"
  },
  {
    "event_id": 9203,
    "Description": "Defeat Boss m30_03 Catacombs 1-4"
  },
  {
    "event_id": 9204,
    "Description": "Defeat Boss m30_04 Catacombs 1-5"
  },
  {
    "event_id": 9205,
    "Description": "Defeat Boss m30_05 Catacombs 2-1"
  },
  {
    "event_id": 9206,
    "Description": "Defeat Boss m30_06 Catacombs 2-2"
  },
  {
    "event_id": 9207,
    "Description": "Defeat Boss m30_07 Catacombs 2-3"
  },
  {
    "event_id": 9208,
    "Description": "Defeat Boss m30_08 Catacombs 3-1"
  },
  {
    "event_id": 9209,
    "Description": "Defeat Boss m30_09 Catacombs 3-2"
  },
  {
    "event_id": 9210,
    "Description": "Defeat Boss m30_10 Catacombs 3-3"
  },
  {
    "event_id": 9211,
    "Description": "Defeat Boss m30_11 Catacombs 3-4"
  },
  {
    "event_id": 9212,
    "Description": "Defeat Boss m30_12 Catacombs 3-5"
  },
  {
    "event_id": 9213,
    "Description": "Defeat Boss m30_13 Catacombs 3-6"
  },
  {
    "event_id": 9214,
    "Description": "Defeat Boss m30_14 Catacombs 4-1"
  },
  {
    "event_id": 9215,
    "Description": "Defeat Boss m30_15 Catacombs 4-2"
  },
  {
    "event_id": 9216,
    "Description": "Defeat Boss m30_16 Catacombs 4-3"
  },
  {
    "event_id": 9217,
    "Description": "Defeat Boss m30_17 Catacombs 5-1"
  },
  {
    "event_id": 9218,
    "Description": "Defeat Boss m30_18 Catacombs 5-2"
  },
  {
    "event_id": 9219,
    "Description": "Defeat Boss m30_19 Catacombs 5-3"
  },
  {
    "event_id": 9220,
    "Description": "Defeat Boss m30_20 Catacombs 5-4"
  },
  {
    "event_id": 9221,
    "Description": "Defeat Boss m30_05 Catacombs 2-1 Hidden"
  },
  {
    "event_id": 9222,
    "Description": "Defeat the boss m35_00 Royal capital underground sewer cemetery"
  },
  {
    "event_id": 9230,
    "Description": "Defeat the boss m31_00 Cave 1-1"
  },
  {
    "event_id": 9231,
    "Description": "Defeat the boss m31_01 Cave 1-2"
  },
  {
    "event_id": 9232,
    "Description": "Defeat the boss m31_02 Cave 1-3"
  },
  {
    "event_id": 9233,
    "Description": "Defeat the boss m31_03 Cave 1-4"
  },
  {
    "event_id": 9234,
    "Description": "Defeat the boss m31_15 Cave 1-5"
  },
  {
    "event_id": 9235,
    "Description": "Defeat the boss m31_17 Cave 1-6"
  },
  {
    "event_id": 9236,
    "Description": "Defeat boss m31_04 Cave 2-1"
  },
  {
    "event_id": 9237,
    "Description": "Defeat boss m31_05 Cave 2-2"
  },
  {
    "event_id": 9238,
    "Description": "Defeat the boss m31_06 Cave 2-3"
  },
  {
    "event_id": 9239,
    "Description": "Defeat boss m31_07 Cave 3-1"
  },
  {
    "event_id": 9240,
    "Description": "Defeat the boss m31_09 Cave 3-2"
  },
  {
    "event_id": 9241,
    "Description": "Defeat the boss m31_18 Cave 3-3"
  },
  {
    "event_id": 9242,
    "Description": "Defeat the boss m31_19 Cave 3-4"
  },
  {
    "event_id": 9243,
    "Description": "Defeat boss m31_10 Cave 4-1"
  },
  {
    "event_id": 9244,
    "Description": "Defeat the boss m31_11 Cave 4-2"
  },
  {
    "event_id": 9245,
    "Description": "Defeat boss m31_20 Cave 4-3"
  },
  {
    "event_id": 9246,
    "Description": "Defeat the boss m31_21 Cave 4-4"
  },
  {
    "event_id": 9247,
    "Description": "Defeat the boss m31_12 Cave 5-1"
  },
  {
    "event_id": 9248,
    "Description": "Defeat the boss m31_22 Cave 5-2"
  },
  {
    "event_id": 9249,
    "Description": "Defeat the boss m31_19 Cave 3-4 Hidden"
  },
  {
    "event_id": 9260,
    "Description": "Defeat the boss m32_00 Mine 1-1"
  },
  {
    "event_id": 9261,
    "Description": "Defeat the boss m32_01 Mine 1-2"
  },
  {
    "event_id": 9262,
    "Description": "Defeat the boss m32_02 Mine 2-1"
  },
  {
    "event_id": 9263,
    "Description": "Defeat the boss m32_04 Mine 3-1"
  },
  {
    "event_id": 9264,
    "Description": "Defeat the boss m34_12 Mine 3-2"
  },
  {
    "event_id": 9265,
    "Description": "Defeat the boss m32_05 Mine 3-3"
  },
  {
    "event_id": 9266,
    "Description": "Defeat the boss m32_07 Mine 4-1"
  },
  {
    "event_id": 9267,
    "Description": "Defeat the boss m32_08 Mine 4-2"
  },
  {
    "event_id": 9268,
    "Description": "Defeat the boss m32_11 Mine 5-1"
  },
  {
    "event_id": 9269,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61100,
    "Description": "Defeat the Boss: Cursed Demon Margit"
  },
  {
    "event_id": 61101,
    "Description": "Boss Defeat: Grafting King"
  },
  {
    "event_id": 61102,
    "Description": "Defeat the boss: Edinburgh"
  },
  {
    "event_id": 61103,
    "Description": "Boss Defeat: Grafted Spider"
  },
  {
    "event_id": 61104,
    "Description": "Defeat the boss: Cursed Demon Margit (Serious)"
  },
  {
    "event_id": 61105,
    "Description": "Boss Defeat Godfrey (Godfrey)"
  },
  {
    "event_id": 61106,
    "Description": "Defeat Boss Lord Hyakuchi"
  },
  {
    "event_id": 61107,
    "Description": "Boss Defeat Godfrey"
  },
  {
    "event_id": 61108,
    "Description": "Defeat Boss Ancestral Spirit"
  },
  {
    "event_id": 61109,
    "Description": "Boss Defeat: The Desolate"
  },
  {
    "event_id": 61110,
    "Description": "Defeat the Boss: Gargoyle Tag Team of the Royal Capital"
  },
  {
    "event_id": 61111,
    "Description": "Defeat Boss: Guardian's Descendant (Death)"
  },
  {
    "event_id": 61112,
    "Description": "Boss Defeat Greater Demon"
  },
  {
    "event_id": 61113,
    "Description": "Defeat the Boss: Underground Labyrinth Ark"
  },
  {
    "event_id": 61114,
    "Description": "Boss Defeat God Skin Tag Team"
  },
  {
    "event_id": 61115,
    "Description": "Defeat Boss Ancient Dragon"
  },
  {
    "event_id": 61116,
    "Description": "Defeat Boss Mariquez"
  },
  {
    "event_id": 61117,
    "Description": "Defeat the boss God Skin"
  },
  {
    "event_id": 61118,
    "Description": "Defeat the boss: Lenara"
  },
  {
    "event_id": 61119,
    "Description": "Defeat Boss Tree Guard (Mage)"
  },
  {
    "event_id": 61120,
    "Description": "Defeat Boss Marenia"
  },
  {
    "event_id": 61121,
    "Description": "Defeat the Boss: Volcano Prison Boss"
  },
  {
    "event_id": 61122,
    "Description": "Boss Defeat: King of Ghouls"
  },
  {
    "event_id": 61123,
    "Description": "Defeat the boss Last boss"
  },
  {
    "event_id": 61124,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61125,
    "Description": "Defeat the boss: Underground Sewer Boss of the Royal Capital"
  },
  {
    "event_id": 61126,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61127,
    "Description": "Boss Defeat Tutorial"
  },
  {
    "event_id": 61128,
    "Description": "Boss Defeat Tutorial EX"
  },
  {
    "event_id": 61129,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61130,
    "Description": "Boss Defeat Ladahn"
  },
  {
    "event_id": 61131,
    "Description": "Defeat the Boss Giant"
  },
  {
    "event_id": 61132,
    "Description": "Defeat Boss Ancestral Spirit (Weak)"
  },
  {
    "event_id": 61133,
    "Description": "Defeat Boss Ancestral Spirit (Strong)"
  },
  {
    "event_id": 61134,
    "Description": "Defeat the boss: Transforming Slime"
  },
  {
    "event_id": 61135,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61170,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61171,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61172,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61173,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61174,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61175,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61180,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61181,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61182,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61183,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61184,
    "Description": "Defeat the boss"
  },
  {
    "event_id": 61200,
    "Description": "Defeat Boss m30_00 Catacombs 1-1"
  },
  {
    "event_id": 61201,
    "Description": "Defeat Boss m30_01 Catacombs 1-2"
  },
  {
    "event_id": 61202,
    "Description": "Defeat Boss m30_02 Catacombs 1-3"
  },
  {
    "event_id": 61203,
    "Description": "Defeat Boss m30_03 Catacombs 1-4"
  },
  {
    "event_id": 61204,
    "Description": "Defeat Boss m30_04 Catacombs 1-5"
  },
  {
    "event_id": 61205,
    "Description": "Defeat Boss m30_05 Catacombs 2-1"
  },
  {
    "event_id": 61206,
    "Description": "Defeat Boss m30_06 Catacombs 2-2"
  },
  {
    "event_id": 61207,
    "Description": "Defeat Boss m30_07 Catacombs 2-3"
  },
  {
    "event_id": 61208,
    "Description": "Defeat Boss m30_08 Catacombs 3-1"
  },
  {
    "event_id": 61209,
    "Description": "Defeat Boss m30_09 Catacombs 3-2"
  },
  {
    "event_id": 61210,
    "Description": "Defeat Boss m30_10 Catacombs 3-3"
  },
  {
    "event_id": 61211,
    "Description": "Defeat Boss m30_11 Catacombs 3-4"
  },
  {
    "event_id": 61212,
    "Description": "Defeat Boss m30_12 Catacombs 3-5"
  },
  {
    "event_id": 61213,
    "Description": "Defeat Boss m30_13 Catacombs 3-6"
  },
  {
    "event_id": 61214,
    "Description": "Defeat Boss m30_14 Catacombs 4-1"
  },
  {
    "event_id": 61215,
    "Description": "Defeat Boss m30_15 Catacombs 4-2"
  },
  {
    "event_id": 61216,
    "Description": "Defeat Boss m30_16 Catacombs 4-3"
  },
  {
    "event_id": 61217,
    "Description": "Defeat Boss m30_17 Catacombs 5-1"
  },
  {
    "event_id": 61218,
    "Description": "Defeat Boss m30_18 Catacombs 5-2"
  },
  {
    "event_id": 61219,
    "Description": "Defeat Boss m30_19 Catacombs 5-3"
  },
  {
    "event_id": 61220,
    "Description": "Defeat Boss m30_20 Catacombs 5-4"
  },
  {
    "event_id": 61230,
    "Description": "Defeat the boss m31_00 Cave 1-1"
  },
  {
    "event_id": 61231,
    "Description": "Defeat the boss m31_01 Cave 1-2"
  },
  {
    "event_id": 61232,
    "Description": "Defeat the boss m31_02 Cave 1-3"
  },
  {
    "event_id": 61233,
    "Description": "Defeat the boss m31_03 Cave 1-4"
  },
  {
    "event_id": 61234,
    "Description": "Defeat the boss m31_15 Cave 1-5"
  },
  {
    "event_id": 61235,
    "Description": "Defeat the boss m31_17 Cave 1-6"
  },
  {
    "event_id": 61236,
    "Description": "Defeat boss m31_04 Cave 2-1"
  },
  {
    "event_id": 61237,
    "Description": "Defeat boss m31_05 Cave 2-2"
  },
  {
    "event_id": 61238,
    "Description": "Defeat the boss m31_06 Cave 2-3"
  },
  {
    "event_id": 61239,
    "Description": "Defeat boss m31_07 Cave 3-1"
  },
  {
    "event_id": 61240,
    "Description": "Defeat the boss m31_09 Cave 3-2"
  },
  {
    "event_id": 61241,
    "Description": "Defeat the boss m31_18 Cave 3-3"
  },
  {
    "event_id": 61242,
    "Description": "Defeat the boss m31_19 Cave 3-4"
  },
  {
    "event_id": 61243,
    "Description": "Defeat boss m31_10 Cave 4-1"
  },
  {
    "event_id": 61244,
    "Description": "Defeat the boss m31_11 Cave 4-2"
  },
  {
    "event_id": 61245,
    "Description": "Defeat boss m31_20 Cave 4-3"
  },
  {
    "event_id": 61246,
    "Description": "Defeat the boss m31_21 Cave 4-4"
  },
  {
    "event_id": 61247,
    "Description": "Defeat the boss m31_12 Cave 5-1"
  },
  {
    "event_id": 61248,
    "Description": "Defeat the boss m31_22 Cave 5-2"
  },
  {
    "event_id": 61260,
    "Description": "Defeat the boss m32_00 Mine 1-1"
  },
  {
    "event_id": 61261,
    "Description": "Defeat the boss m32_01 Mine 1-2"
  },
  {
    "event_id": 61262,
    "Description": "Defeat the boss m32_02 Mine 2-1"
  },
  {
    "event_id": 61263,
    "Description": "Defeat the boss m32_04 Mine 3-1"
  },
  {
    "event_id": 61264,
    "Description": "Defeat the boss m34_12 Mine 3-2"
  },
  {
    "event_id": 61265,
    "Description": "Defeat the boss m32_05 Mine 3-3"
  },
  {
    "event_id": 61266,
    "Description": "Defeat the boss m32_07 Mine 4-1"
  },
  {
    "event_id": 61267,
    "Description": "Defeat the boss m32_08 Mine 4-2"
  },
  {
    "event_id": 61268,
    "Description": "Defeat the boss m32_11 Mine 5-1"
  },
  {
    "event_id": 61261,
    "Description": "Defeat the boss"
  }
]
