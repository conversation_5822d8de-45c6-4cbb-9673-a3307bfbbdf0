[{"event_id": 9100, "Description": "Defeated <PERSON><PERSON> the Grafted", "is_defeated": false}, {"event_id": 9101, "Description": "Defeated Margit, the Fell Omen", "is_defeated": false}, {"event_id": 9103, "Description": "Defeated Grafted Scion", "is_defeated": true}, {"event_id": 9104, "Description": "Defeated <PERSON><PERSON><PERSON>, the Omen King", "is_defeated": false}, {"event_id": 9105, "Description": "Defeated <PERSON>, First Elden Lord", "is_defeated": false}, {"event_id": 9106, "Description": "Defeated Sir <PERSON><PERSON><PERSON>_ideon Of<PERSON>, the All-Knowing", "is_defeated": false}, {"event_id": 9107, "Description": "Defeated <PERSON><PERSON><PERSON>", "is_defeated": false}, {"event_id": 9108, "Description": "Defeated <PERSON><PERSON>, Naturalborn of the Voevent_id", "is_defeated": false}, {"event_id": 9109, "Description": "Defeated Dragonkin Soldier of Nokstella", "is_defeated": false}, {"event_id": 9111, "Description": "Defeated Lichdragon Fortissax", "is_defeated": true}, {"event_id": 9112, "Description": "Defeated <PERSON><PERSON><PERSON>, Lord of Blood", "is_defeated": false}, {"event_id": 9115, "Description": "Defeated Dragonlord Placevent_idusax", "is_defeated": true}, {"event_id": 9116, "Description": "Defeated Maliketh, the Black Blade", "is_defeated": true}, {"event_id": 9118, "Description": "Defeated <PERSON><PERSON><PERSON>, Queen of the Full Moon", "is_defeated": true}, {"event_id": 9119, "Description": "Defeated Royal Knight Loretta", "is_defeated": false}, {"event_id": 9120, "Description": "Defeated <PERSON><PERSON>, Blade of Miquella", "is_defeated": false}, {"event_id": 9122, "Description": "Defeated <PERSON><PERSON><PERSON>, Lord of Blasphemy", "is_defeated": true}, {"event_id": 9123, "Description": "Defeated the Elden Beast", "is_defeated": false}, {"event_id": 9130, "Description": "Defeated Starscourge Radahn", "is_defeated": true}, {"event_id": 9131, "Description": "Defeated Fire Giant", "is_defeated": false}, {"event_id": 9133, "Description": "Defeated Regal Ancestor Spirit", "is_defeated": true}, {"event_id": 9134, "Description": "Defeated Dragonkin Soldier - Lake of Rot", "is_defeated": true}, {"event_id": 9135, "Description": "Defeated Fia's Champion", "is_defeated": true}, {"event_id": 9182, "Description": "Defeated <PERSON><PERSON><PERSON> of the Briar", "is_defeated": false}, {"event_id": 9184, "Description": "Defeated Commander <PERSON>", "is_defeated": true}, {"event_id": 9143, "Description": "Defeated <PERSON><PERSON><PERSON>, Consort of <PERSON><PERSON><PERSON>", "is_defeated": true}, {"event_id": 9146, "Description": "Defeated Messmer the Impaler", "is_defeated": true}, {"event_id": 9164, "Description": "Defeated Commander Gaius", "is_defeated": false}, {"event_id": 9162, "Description": "Defeated Scadutree Avatar", "is_defeated": true}, {"event_id": 9190, "Description": "Defeated <PERSON><PERSON><PERSON>, <PERSON>", "is_defeated": false}, {"event_id": 9160, "Description": "Defeated <PERSON><PERSON><PERSON>, Saint of the Bud", "is_defeated": false}, {"event_id": 9140, "Description": "Defeated Divine Beast Dancing Lion", "is_defeated": true}, {"event_id": 9156, "Description": "Defeated <PERSON><PERSON>_idra, Lord of Frenzied Flame", "is_defeated": true}, {"event_id": 9155, "Description": "Defeated <PERSON><PERSON>, Mother of Fingers", "is_defeated": true}, {"event_id": 9148, "Description": "Defeated Putrescent Knight", "is_defeated": true}, {"event_id": 10000800, "Description": "Boss Defeated: <PERSON><PERSON> the <PERSON>ted", "is_defeated": false}, {"event_id": 10000850, "Description": "Boss Defeated: <PERSON><PERSON> the Fell Omen", "is_defeated": true}, {"event_id": 10010800, "Description": "Boss Defeated: <PERSON><PERSON> (Chapel of Anticipation)", "is_defeated": true}, {"event_id": 11000800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>, the Omen King", "is_defeated": false}, {"event_id": 11000850, "Description": "Boss Defeated: <PERSON>, <PERSON><PERSON>", "is_defeated": true}, {"event_id": 11050800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>", "is_defeated": true}, {"event_id": 11050850, "Description": "Boss Defeated: Sir <PERSON>_ideon Ofnir", "is_defeated": false}, {"event_id": 12010800, "Description": "Boss Defeated: <PERSON><PERSON> Soldier of Nokstella", "is_defeated": true}, {"event_id": 12010850, "Description": "Boss Defeated: <PERSON><PERSON> (Lake of Rot)", "is_defeated": true}, {"event_id": 12020800, "Description": "Boss Defeated: <PERSON><PERSON> Gargoyles", "is_defeated": false}, {"event_id": 12020830, "Description": "Boss Defeated: <PERSON><PERSON> Soldier of Nokron", "is_defeated": false}, {"event_id": 12020850, "Description": "Boss Defeated: <PERSON><PERSON>", "is_defeated": true}, {"event_id": 12020390, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>", "is_defeated": false}, {"event_id": 12030800, "Description": "Boss Defeated: Fia's Champion", "is_defeated": false}, {"event_id": 12030850, "Description": "Boss Defeated: Lichdragon Fortissax", "is_defeated": false}, {"event_id": 12040800, "Description": "Boss Defeated: <PERSON><PERSON>, Naturalborn of the Voevent_id", "is_defeated": false}, {"event_id": 12050800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>, Lord of Blood", "is_defeated": true}, {"event_id": 12080800, "Description": "Boss Defeated: Ancestor Spirit", "is_defeated": false}, {"event_id": 12090800, "Description": "Boss Defeated: Regal Ancestor Spirit", "is_defeated": false}, {"event_id": 13000800, "Description": "Boss Defeated: <PERSON><PERSON>, the Black Blade", "is_defeated": false}, {"event_id": 13000830, "Description": "Boss Defeated: <PERSON><PERSON>vent_idusax", "is_defeated": true}, {"event_id": 13000850, "Description": "Boss Defeated: Godskin Duo", "is_defeated": true}, {"event_id": 14000800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>, Queen of the Full Moon", "is_defeated": true}, {"event_id": 14000850, "Description": "Boss Defeated: <PERSON> of Radagon", "is_defeated": false}, {"event_id": 15000800, "Description": "Boss Defeated: <PERSON><PERSON>, <PERSON> of Miquella", "is_defeated": false}, {"event_id": 15000850, "Description": "Boss Defeated: <PERSON><PERSON>, Knight of the Haligtree", "is_defeated": false}, {"event_id": 16000800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>, Lord of Blasphemy", "is_defeated": false}, {"event_id": 16000850, "Description": "Boss Defeated: <PERSON><PERSON>", "is_defeated": false}, {"event_id": 16000860, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>", "is_defeated": true}, {"event_id": 18000800, "Description": "Boss Defeated: Ulcerated Tree Spirit", "is_defeated": false}, {"event_id": 18000850, "Description": "Boss Defeated: Soldier of Godrick", "is_defeated": true}, {"event_id": 19000800, "Description": "Boss Defeated: <PERSON>den <PERSON>", "is_defeated": true}, {"event_id": 20000800, "Description": "Boss Defeated: Divine Beast Dancing Lion", "is_defeated": true}, {"event_id": 20010800, "Description": "Boss Defeated: Promised Consort <PERSON><PERSON><PERSON>", "is_defeated": true}, {"event_id": 21000850, "Description": "Boss Defeated: Golden Hippopotamus", "is_defeated": false}, {"event_id": 21010800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON> the Impaler", "is_defeated": true}, {"event_id": 22000800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON>", "is_defeated": false}, {"event_id": 25000800, "Description": "Boss Defeated: <PERSON><PERSON>, Mother of Fingers", "is_defeated": false}, {"event_id": 28000800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON><PERSON>, Lord of Frenzied Flame", "is_defeated": true}, {"event_id": 30000800, "Description": "Boss Defeated: Cemetery Shade (Tombsward Catacombs)", "is_defeated": false}, {"event_id": 30010800, "Description": "Boss Defeated: <PERSON>rd<PERSON> Burial Watchdog (Impaler's Catacombs)", "is_defeated": true}, {"event_id": 30020800, "Description": "Boss Defeated: <PERSON>rd<PERSON> Burial Watchdog (Stormfoot Catacombs)", "is_defeated": false}, {"event_id": 30030800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON> (Road's End Catacombs)", "is_defeated": false}, {"event_id": 30040800, "Description": "Boss Defeated: Grave Warden Duelist (Murkwater Catacombs)", "is_defeated": false}, {"event_id": 30050800, "Description": "Boss Defeated: Cemetery Shade (Black Knife Catacombs)", "is_defeated": true}, {"event_id": 30050850, "Description": "Boss Defeated: Black Knife <PERSON> (Black Knife Catacombs)", "is_defeated": false}, {"event_id": 30060800, "Description": "Boss Defeated: <PERSON>rd<PERSON> Burial Watchdog (Cliffbottom Catacombs)", "is_defeated": true}, {"event_id": 30070800, "Description": "Boss Defeated: <PERSON>rd<PERSON> Burial Watchdog (Wyndham Catacombs)", "is_defeated": true}, {"event_id": 30080800, "Description": "Boss Defeated: Ancient Hero of Zamor (Sainted Hero's Grave)", "is_defeated": true}, {"event_id": 30090800, "Description": "Boss Defeated: <PERSON> of the Champion (<PERSON><PERSON><PERSON>'s Grave)", "is_defeated": true}, {"event_id": 30100800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON> (Auriza Hero's Grave)", "is_defeated": false}, {"event_id": 30110800, "Description": "Boss Defeated: <PERSON> K<PERSON> (Deathtouched Catacombs)", "is_defeated": true}, {"event_id": 30120800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON> (Unsightly Catacombs)", "is_defeated": false}, {"event_id": 30130800, "Description": "Boss Defeated: Grave Warden Duelist (Auriza Sevent_ide Tomb)", "is_defeated": false}, {"event_id": 30140800, "Description": "Boss Defeated: Erdtree Burial Watchdog (Minor Erdtree Catacombs)", "is_defeated": false}, {"event_id": 30150800, "Description": "Boss Defeated: Cemetery Shade (Caelevent_id Catacombs)", "is_defeated": true}, {"event_id": 30160800, "Description": "Boss Defeated: Putrevent_id Tree Spirit (War-Dead Catacombs)", "is_defeated": false}, {"event_id": 30170800, "Description": "Boss Defeated: Ancient Hero of Zamor (Giant-Conquering Hero's Grave)", "is_defeated": true}, {"event_id": 30180800, "Description": "Boss Defeated: Ulcerated Tree Sprit (Giants' Mountaintop Catacombs)", "is_defeated": false}, {"event_id": 30190800, "Description": "Boss Defeated: Putrevent_id Grave Warden Duelist (Consecrated Snowfield Catacombs)", "is_defeated": false}, {"event_id": 30200810, "Description": "Boss Defeated: <PERSON><PERSON> Tear (Hevent_idden Path to the Haligtree)", "is_defeated": true}, {"event_id": 31000800, "Description": "Boss Defeated: <PERSON><PERSON> (Murkwater Cave)", "is_defeated": false}, {"event_id": 31010800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON> (Earthbore Cave)", "is_defeated": true}, {"event_id": 31020800, "Description": "Boss Defeated: <PERSON> the Blighted Bloom (Tombsward Cave)", "is_defeated": false}, {"event_id": 31030800, "Description": "Boss Defeated: Beast<PERSON> of Farum Azula (Grovesevent_ide Cave)", "is_defeated": true}, {"event_id": 31040800, "Description": "Boss Defeated: <PERSON><PERSON> (Stillwater Cave)", "is_defeated": false}, {"event_id": 31050800, "Description": "Boss Defeated: Bloodhound Knight (Lakesevent_ide Crystal Cave)", "is_defeated": false}, {"event_id": 31060800, "Description": "Boss Defeated: <PERSON><PERSON> (Academy Crystal Cave)", "is_defeated": false}, {"event_id": 31070800, "Description": "Boss Defeated: <PERSON><PERSON> of Rot (Seethewater Cave)", "is_defeated": true}, {"event_id": 31090800, "Description": "Boss Defeated: <PERSON>mi-Human Queen <PERSON> (Volcano Cave)", "is_defeated": true}, {"event_id": 31100800, "Description": "Boss Defeated: Beast<PERSON> of Farum Azula (Dragonbarrow Cave)", "is_defeated": true}, {"event_id": 31110800, "Description": "Boss Defeated: <PERSON><PERSON> (<PERSON><PERSON><PERSON>_ideaway)", "is_defeated": true}, {"event_id": 31120800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON><PERSON> Crusader (Cave of the Forlorn)", "is_defeated": false}, {"event_id": 31150800, "Description": "Boss Defeated: <PERSON><PERSON>-Human Chief (Coastal Cave)", "is_defeated": false}, {"event_id": 31170800, "Description": "Boss Defeated: <PERSON> (Highroad Cave)", "is_defeated": false}, {"event_id": 31180800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON> (<PERSON><PERSON><PERSON>'s Grotto)", "is_defeated": true}, {"event_id": 31190800, "Description": "Boss Defeated: <PERSON> (Sage's Cave)", "is_defeated": false}, {"event_id": 31190850, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON><PERSON> (Sage's Cave)", "is_defeated": true}, {"event_id": 31200800, "Description": "Boss Defeated: <PERSON><PERSON> (Abandoned Cave)", "is_defeated": true}, {"event_id": 31210800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON> Duelist (Gaol Cave)", "is_defeated": false}, {"event_id": 31220800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON><PERSON> (Spiritcaller's Cave)", "is_defeated": false}, {"event_id": 32000800, "Description": "Boss Defeated: <PERSON><PERSON> (Morne Tunnel)", "is_defeated": false}, {"event_id": 32010800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON> Troll (Limgrave Tunnels)", "is_defeated": false}, {"event_id": 32020800, "Description": "Boss Defeated: Crystalian <PERSON> Ringblade (Raya Lucaria Crystal Tunnel)", "is_defeated": false}, {"event_id": 32040800, "Description": "Boss Defeated: <PERSON><PERSON><PERSON> Troll (Old Altus Tunnel)", "is_defeated": true}, {"event_id": 32050800, "Description": "Boss Defeated: <PERSON><PERSON> <PERSON> Ringblade/Spear (Altus Tunnel)", "is_defeated": true}, {"event_id": 32070800, "Description": "Boss Defeated: Magma Wyrm (Gael Tunnel)", "is_defeated": false}, {"event_id": 32080800, "Description": "Boss Defeated: Fallingstar Beast (Sellia Crystal Tunnel)", "is_defeated": false}, {"event_id": 32110800, "Description": "Boss Defeated: <PERSON><PERSON>, Stars of Darkness", "is_defeated": false}, {"event_id": 34120800, "Description": "Boss Defeated: Onyx Lord (Divine Tower of West Altus)", "is_defeated": false}]