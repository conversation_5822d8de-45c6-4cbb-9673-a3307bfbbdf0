import json
import hashlib
import struct
import argparse # Added for command-line arguments
from Crypto.Cipher import AES

# Toto je zjednodu<PERSON><PERSON> verze, vynechává skutečné SL2 parsování a dešifrování
# (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, přid<PERSON>v<PERSON>me parsování hlaviček SL2)
# a př<PERSON><PERSON>kl<PERSON>d<PERSON>, že decrypted_event_flags_array je již k dispozici.

# !!! KRITICKÉ NASTAVENÍ !!!
# EVENT_FLAGS_START_OFFSET určuje, kde v dešifrovaném bloku dat slotu začíná skutečná sekce event flagů.
# Tato hodnota MUSÍ být správně zjištěna pro přesné výsledky.
# Hodnota 0x190010 je POUZE PLACEHOLDER a pravděpodobně bude potřeba ji upravit.
# Běžn<PERSON> hodnoty pro začátek hlavních dat save souboru jsou např. 0x10, 0x190010.
# Samotné event flagy mohou být dále uvnitř tohoto bloku.
DEFAULT_EVENT_FLAGS_START_OFFSET = 0x10090 # Trying another offset based on community discussions
RUST_LIB_EVENT_FLAGS_SIZE = 0x1BF99F # Size of the event_flags Vec<u8> from er-save-lib UserDataX
RUST_LIB_FLAG_DIVISOR = 1000
RUST_LIB_BLOCK_SIZE_BYTES = 125 # 1000 flags / 8 bits_per_byte

# We'll use RUST_LIB_EVENT_FLAGS_SIZE for extracting the event_flags_actual_data
# EVENT_FLAGS_SIZE will now refer to this new constant for clarity in the code.
EVENT_FLAGS_SIZE = RUST_LIB_EVENT_FLAGS_SIZE

def load_boss_data(json_filepath):
    with open(json_filepath, 'r', encoding='utf-8') as f:
        return json.load(f)

def load_event_flag_map_from_bst(bst_filepath):
    # Tuto funkci bys měl mít z process_event_flags.py nebo podobně
    # Příklad:
    event_map = {}
    with open(bst_filepath, 'r') as f:
        for line in f:
            if line.strip() and not line.startswith("#"):
                parts = line.strip().split(',')
                if len(parts) == 2:
                    try:
                        block_id = int(parts[0])
                        offset = int(parts[1])
                        event_map[block_id] = offset
                    except ValueError:
                        print(f"Chyba při parsování řádku v BST: {line.strip()}")
    return event_map

def get_event_flag_byte_offset_and_bit(event_id, event_flag_map):
    """
    Calculates the byte offset and bit index for an event_id based on er-save-lib logic.
    """
    # Determine the logical block ID and the index within that logical block
    # This corresponds to: block = event_id / FLAG_DIVISOR
    #                     index = event_id - block * FLAG_DIVISOR
    # in the Rust code, which is equivalent to event_id % FLAG_DIVISOR
    logical_block_id = event_id // RUST_LIB_FLAG_DIVISOR
    index_within_logical_block = event_id % RUST_LIB_FLAG_DIVISOR

    # Get the multiplier from the event_flag_map (loaded from eventflag_bst.txt)
    # This corresponds to: Self::event_flag_map().get(&block)
    # The value from the map is used to calculate the start of the physical 125-byte block.
    bst_block_value = event_flag_map.get(logical_block_id)

    if bst_block_value is None:
        print(f"DEBUG: Logical Block ID {logical_block_id} (from event_id {event_id}) nenalezen v event_flag_map.")
        return None, None

    # Calculate the starting byte offset of the physical block within the event_flags vector
    # This corresponds to: offset = res * BLOCK_SIZE
    physical_block_start_offset_bytes = bst_block_value * RUST_LIB_BLOCK_SIZE_BYTES
    
    # Calculate the byte index within this physical block
    # This corresponds to: byte_index = index / 8
    byte_offset_within_physical_block = index_within_logical_block // 8
    
    # Calculate the final byte offset within the entire event_flags_actual_data array
    final_byte_offset = physical_block_start_offset_bytes + byte_offset_within_physical_block
    
    # Calculate the natural bit index (0-7) within the target byte
    # This corresponds to: bit_index = index - byte_index * 8
    natural_bit_index = index_within_logical_block % 8
    
    # Reverse the bit index to match er-save-lib's logic (7 - bit_index)
    # (Most significant bit is often considered bit 7, least significant is bit 0)
    final_bit_index = 7 - natural_bit_index
    
    # print(f"DEBUG: event_id={event_id} -> logical_block_id={logical_block_id}, index_in_logical_block={index_within_logical_block}")
    # print(f"DEBUG: bst_block_value={bst_block_value}, physical_block_start_offset_bytes={physical_block_start_offset_bytes}")
    # print(f"DEBUG: byte_offset_within_physical_block={byte_offset_within_physical_block}, natural_bit_index={natural_bit_index}")
    # print(f"DEBUG: final_byte_offset={final_byte_offset}, final_bit_index={final_bit_index}")

    return final_byte_offset, final_bit_index

def check_boss_status_from_decrypted_data(event_id, event_flag_map, decrypted_event_flags_array):
    final_byte_offset, bit_index_in_byte = get_event_flag_byte_offset_and_bit(event_id, event_flag_map)

    if final_byte_offset is None:
        # DEBUG message for "Block ID not found" is already printed by get_event_flag_byte_offset_and_bit
        return None # Indicates mapping error, boss will be excluded

    if not decrypted_event_flags_array or final_byte_offset >= len(decrypted_event_flags_array):
        print(f"DEBUG: Vypočítaný offset {final_byte_offset} je mimo rozsah platných event flagů (délka: {len(decrypted_event_flags_array) if decrypted_event_flags_array else 0}) pro event_id {event_id}. Nelze určit status.")
        return None # Indicates offset error or no flags data, boss will be excluded

    byte_value = decrypted_event_flags_array[final_byte_offset]
    is_set = (byte_value >> bit_index_in_byte) & 1
    
    return bool(is_set)

# AES Key from DSR Unpacker - assuming it's the same or similar for ER
AES_KEY = bytes([
    0x01, 0x23, 0x45, 0x67, 0x89, 0xAB, 0xCD, 0xEF,
    0xFE, 0xDC, 0xBA, 0x98, 0x76, 0x54, 0x32, 0x10
])

def decrypt_slot_data(encryption_iv, encrypted_data, key):
    """
    Decrypts the provided data using AES CBC mode.
    """
    try:
        cipher = AES.new(key, AES.MODE_CBC, iv=encryption_iv)
        decrypted_data = cipher.decrypt(encrypted_data)
        # Potentially remove PKCS7 padding if it was applied, though save files might not use standard padding.
        # For now, we return the full block. The actual event flags are a sub-segment.
        return decrypted_data
    except Exception as e:
        print(f"Chyba při dešifrování dat slotu: {e}")
        return None

def _get_encrypted_slot_data_and_iv(sl2_filepath, character_slot_index):
    """
    Parses the SL2 (BND4) file to extract the encryption IV and encrypted data for a character slot.
    Based on DSR SL2 Unpacker structure. Assumes Elden Ring SL2 is similar.
    """
    try:
        with open(sl2_filepath, "rb") as f:
            # 1. Parse SaveFileHeader (BND4 header) - 64 bytes
            # struct SaveFileHeader {
            #     uint32_t magic32_0 = 0x34444E42; // "BND4"
            #     uint32_t magic32_1 = 0x00000000;
            #     uint32_t magic32_2 = 0x00010000;
            #     uint32_t slot_count = 11;
            #     uint32_t const32b_0 = 0x00000040; // Offset to slot headers or size of this part
            #     uint32_t const32b_1 = 0x00000000;
            #     uint32_t const32b_2 = 0x00000000;
            #     uint32_t const32b_3 = 0x00000000;
            #     uint32_t slot_header_size = 0x00000020; // Size of one SaveSlotHeader
            #     uint32_t const32b_4 = 0x00000000;
            #     uint32_t size = 0x000002C0; // Size of the file header (Including slot header array and title arrays)
            #     uint32_t const32b_5 = 0x00000000;
            #     uint32_t const32b_6 = 0x00002001;
            #     uint8_t padding[12];
            # }
            header_data = f.read(64)
            if len(header_data) < 64:
                print("Chyba: SL2 soubor je příliš krátký pro hlavičku.")
                return None, None
            
            # Using little-endian '<'
            # 13 unsigned ints (I) and 12 bytes (12s)
            # magic0, magic1, magic2, slot_count, const0, const1, const2, const3, 
            # slot_hdr_size, const4, total_hdr_size, const5, const6, padding[12s]
            # Correct format: 13 * I = 52 bytes. Padding is 12 bytes. Total 64.
            # So it's <13I12s
            parsed_header = struct.unpack("<13I12s", header_data)
            
            magic0 = parsed_header[0]
            slot_count = parsed_header[3]
            # slot_header_entry_size = parsed_header[8] # Should be 0x20 (32)
            # total_header_block_size = parsed_header[10] # Should be 0x2C0 (704)

            if magic0 != 0x34444E42: # "BND4"
                print(f"Chyba: Neplatný magic number v SL2 souboru. Očekáváno 0x34444E42, nalezeno {magic0:X}")
                return None, None
            
            print(f"SL2 Hlavička: Magic='BND4', Počet slotů celkem: {slot_count}")

            if not (0 <= character_slot_index < slot_count -1): # Last slot is usually menu
                 print(f"Chyba: Neplatný index slotu postavy: {character_slot_index}. Max: {slot_count - 2}")
                 return None, None

            # 2. Calculate offset to the target SaveSlotHeader
            # SaveSlotHeaders start after the initial 64-byte SaveFileHeader.
            slot_header_offset = 64 + (character_slot_index * 32) # 32 is sizeof(SaveSlotHeader)
            
            f.seek(slot_header_offset)
            slot_header_data = f.read(32)
            if len(slot_header_data) < 32:
                print(f"Chyba: Nelze přečíst hlavičku slotu {character_slot_index}.")
                return None, None

            # struct SaveSlotHeader { // 32 bytes
            #     const uint32_t const32b_0 = 0x00000050;
            #     const uint32_t const32b_1 = 0xFFFFFFFF;
            #     const uint32_t slot_size = 0x060030;
            #     const uint32_t const32b_2 = 0x00000000;
            #     uint32_t offset = 0; // Offset (from start of file) of save slot struct (SaveEntry)
            #     uint32_t title_offset = 0;
            #     const uint32_t padding_size = 0x00000000;
            #     const uint32_t const32b_3 = 0x00000000;
            # }
            # Format: 8 unsigned ints
            parsed_slot_header = struct.unpack("<8I", slot_header_data)
            save_entry_size = parsed_slot_header[2] # Pro Elden Ring by mělo být 0x280010
            save_entry_offset = parsed_slot_header[4]

            # Očekávaná velikost pro Elden Ring. Pokud se liší, vypíše varování.
            expected_er_save_entry_size = 0x280010
            if save_entry_size != expected_er_save_entry_size:
                 print(f"Varování: Očekávaná velikost SaveEntry pro Elden Ring je {expected_er_save_entry_size:X}, ale hlavička slotu udává {save_entry_size:X}")
            
            print(f"Slot {character_slot_index}: Offset SaveEntry: {save_entry_offset:X}, Velikost SaveEntry: {save_entry_size:X}")

            # 4. Read the SaveEntry
            f.seek(save_entry_offset)
            save_entry_data = f.read(save_entry_size)
            if len(save_entry_data) < save_entry_size:
                print(f"Chyba: Nelze přečíst kompletní SaveEntry pro slot {character_slot_index}.")
                return None, None
            
            # 5. Extract IV (first 16 bytes)
            encryption_iv = save_entry_data[:16]
            encrypted_data = save_entry_data[16:]
            
            print(f"Pro slot {character_slot_index}: IV načteno ({len(encryption_iv)} bytů), Šifrovaná data načtena ({len(encrypted_data)} bytů).")
            print(f"DEBUG: Slot {character_slot_index}, Prvních 32 bytů šifrovaných dat: {encrypted_data[:32].hex()}")
            print(f"DEBUG: Slot {character_slot_index}, MD5 šifrovaných dat: {hashlib.md5(encrypted_data).hexdigest()}")
            return encryption_iv, encrypted_data

    except FileNotFoundError:
        print(f"Chyba: SL2 soubor nebyl nalezen na cestě: {sl2_filepath}")
        return None, None
    except Exception as e:
        print(f"Neočekávaná chyba při parsování SL2 souboru: {e}")
        return None, None
# --- Hlavní logika ---
def main():
    parser = argparse.ArgumentParser(description="Analyzuje Elden Ring SL2 save soubor pro statusy bossů.")
    parser.add_argument(
        "--sl2_file",
        type=str,
        default="SavesTest/ER0000.sl2",
        help="Cesta k SL2 save souboru (default: SavesTest/ER0000.sl2)"
    )
    parser.add_argument(
        "--slot_index",
        type=int,
        default=5,
        help="Index slotu postavy (0-9, default: 5)"
    )
    parser.add_argument(
        "--boss_data_json",
        type=str,
        default="data/Bosses/reference_boss_ids.json",
        help="Cesta k JSON souboru s daty bossů (default: data/Bosses/reference_boss_ids.json)"
    )
    parser.add_argument(
        "--event_flag_bst",
        type=str,
        default="data/EventFlags/eventflag_bst.txt",
        help="Cesta k eventflag_bst.txt souboru (default: data/EventFlags/eventflag_bst.txt)"
    )
    parser.add_argument(
        "--event_flags_offset",
        type=lambda x: int(x, 0), # Umožňuje hex (0x...) i dec vstup
        default=DEFAULT_EVENT_FLAGS_START_OFFSET,
        help=f"Hexadecimální nebo decimální offset začátku event flagů v dešifrovaných datech slotu (default: {DEFAULT_EVENT_FLAGS_START_OFFSET:#X})"
    )
    parser.add_argument(
        "--fallback_bin",
        type=str,
        default="SavesTest/decrypted_slot0_eventflags.bin",
        help="Cesta k binárnímu souboru s předem dešifrovanými event flagy pro fallback (default: SavesTest/decrypted_slot0_eventflags.bin)"
    )

    args = parser.parse_args()

    current_event_flags_start_offset = args.event_flags_offset

    # 1. Načtení dat
    all_bosses = load_boss_data(args.boss_data_json)
    event_flag_map = load_event_flag_map_from_bst(args.event_flag_bst)

    event_flags_actual_data = None 

    # Pokus o parsování SL2 souboru
    encryption_iv, encrypted_slot_data = _get_encrypted_slot_data_and_iv(args.sl2_file, args.slot_index)

    if encrypted_slot_data and encryption_iv:
        print(f"Úspěšně extrahována šifrovaná data ({len(encrypted_slot_data)} bytů) a IV ({len(encryption_iv)} bytů) ze souboru {args.sl2_file} pro slot {args.slot_index}.")
        print("Pokouším se dešifrovat data slotu...")
        decrypted_data_full_slot = decrypt_slot_data(encryption_iv, encrypted_slot_data, AES_KEY)
        
        if decrypted_data_full_slot:
            print(f"Data slotu úspěšně dešifrována ({len(decrypted_data_full_slot)} bytů).")
            print(f"DEBUG: Slot {args.slot_index}, Prvních 32 bytů DEŠIFROVANÝCH dat: {decrypted_data_full_slot[:32].hex()}")
            print(f"DEBUG: Slot {args.slot_index}, MD5 DEŠIFROVANÝCH dat: {hashlib.md5(decrypted_data_full_slot).hexdigest()}")
            
            # Extrahujeme skutečný blok event flagů z dešifrovaných dat slotu
            if len(decrypted_data_full_slot) >= current_event_flags_start_offset + EVENT_FLAGS_SIZE:
                event_flags_actual_data = bytearray(
                    decrypted_data_full_slot[current_event_flags_start_offset : current_event_flags_start_offset + EVENT_FLAGS_SIZE]
                )
                print(f"Blok event flagů extrahován: offset={current_event_flags_start_offset:X}, velikost={EVENT_FLAGS_SIZE:X} ({len(event_flags_actual_data)} bytů).")
                if current_event_flags_start_offset == DEFAULT_EVENT_FLAGS_START_OFFSET:
                     print(f"POZNÁMKA: Používá se výchozí (placeholder) EVENT_FLAGS_START_OFFSET: {DEFAULT_EVENT_FLAGS_START_OFFSET:#X}. Pro přesné výsledky jej možná bude nutné upravit pomocí --event_flags_offset.")
                else:
                    print(f"POZNÁMKA: Používá se zadaný EVENT_FLAGS_START_OFFSET: {current_event_flags_start_offset:#X}.")
            else:
                print(f"Chyba: Dešifrovaná data slotu (délka {len(decrypted_data_full_slot)}) jsou příliš krátká pro extrakci event flagů na offsetu {current_event_flags_start_offset:X} s velikostí {EVENT_FLAGS_SIZE:X}.")
                event_flags_actual_data = None
        else:
            print("Dešifrování dat slotu selhalo.")
            event_flags_actual_data = None
    else:
        print(f"Nepodařilo se extrahovat šifrovaná data nebo IV ze SL2 souboru {args.sl2_file}.")
        event_flags_actual_data = None

    if not event_flags_actual_data: # Pokud se nepodařilo získat event flagy z SL2, zkusíme načíst externí .bin soubor
        print(f"Pokouším se načíst event flagy z externího souboru: {args.fallback_bin}")
        try:
            with open(args.fallback_bin, "rb") as f:
                event_flags_actual_data = bytearray(f.read())
            print(f"Úspěšně načtena data event flagů z externího souboru: {args.fallback_bin} ({len(event_flags_actual_data)} bytů).")
            # Ověření, zda velikost odpovídá očekávané EVENT_FLAGS_SIZE
            if len(event_flags_actual_data) != EVENT_FLAGS_SIZE:
                print(f"Varování: Velikost načtených event flagů z souboru ({len(event_flags_actual_data)}) se liší od očekávané EVENT_FLAGS_SIZE ({EVENT_FLAGS_SIZE}).")
        except FileNotFoundError:
            print(f"Externí soubor s daty event flagů ({args.fallback_bin}) nebyl nalezen.")
        except Exception as e:
            print(f"Chyba při načítání externích dat event flagů z {args.fallback_bin}: {e}")


# DŮLEŽITÉ: Nyní pracujeme s `event_flags_actual_data`, což by měl být přesný blok event flagů.
# Logika v `get_event_flag_byte_offset_and_bit` a `check_boss_status_from_decrypted_data`
# bude nyní operovat na tomto (snad) správně vymezeném bloku.

# Příklad: Pokud bys měl dešifrovaná data (toto je jen malý příklad, reálná data jsou větší!)
# Předpokládejme, že eventflag_bst.txt má pro block_id 61 offset 10 (relativně k začátku event_flags_actual_data)
# a pro block_id 100008 offset 50.
# A event_id 61100 je (block 61, index 100), takže byte 10 + (100//8) = 10+12=22, bit 100%8=4
# A event_id 10000850 je (block 100008, index 850), takže byte 50 + (850//8) = 50+106=156, bit 850%8=2
# event_flags_actual_data = bytearray(EVENT_FLAGS_SIZE) # Vytvoří pole nulových bytů o správné velikosti
# if len(event_flags_actual_data) > 22: event_flags_actual_data[22] = 0b00010000 # Nastaví 4. bit pro bosse 61100
# if len(event_flags_actual_data) > 156: event_flags_actual_data[156] = 0b00000100 # Nastaví 2. bit pro bosse 10000850
# TOTO JE JEN ILUSTRACE, POTŘEBUJEŠ REÁLNÁ DATA ZÍSKANÁ SPRÁVNÝM OFFSETEM!

    # Zpracování bossů, pokud máme data event flagů:
    if event_flags_actual_data:
        print(f"DEBUG: Slot {args.slot_index}, Prvních 32 bytů EVENT_FLAGS_ACTUAL_DATA: {event_flags_actual_data[:32].hex()}")
        print(f"DEBUG: Slot {args.slot_index}, MD5 EVENT_FLAGS_ACTUAL_DATA: {hashlib.md5(event_flags_actual_data).hexdigest()}")
        defeated_boss_count = 0
        processed_bosses_data = [] # Seznam pro uložení pouze zpracovatelných bossů
        
        for boss_original in all_bosses:
            # Ignoruj DLC prozatím, pokud máš nějaký způsob, jak je identifikovat
            # např. if boss_original.get("is_dlc", False): continue # Předpokládá 'is_dlc' klíč
            
            boss = boss_original.copy() # Pracujeme s kopií, abychom nemodifikovali původní seznam v paměti
            
            event_id_to_check = boss.get("event_id")
            if event_id_to_check is None:
                event_id_to_check = boss.get("event_event_id") # Fallback na alternativní klíč

            boss_name = boss.get('Description', f'Unknown Boss with ID {event_id_to_check}')

            if event_id_to_check is None:
                print(f"DEBUG: Chybí event_id i event_event_id pro záznam: {boss}")
                continue # Přeskočíme tohoto bosse

            status = check_boss_status_from_decrypted_data(event_id_to_check, event_flag_map, event_flags_actual_data)
            
            if status is not None: # Pokud je status True nebo False (ne None, což značí chybu mapování)
                boss["is_defeated"] = status
                # Ujistíme se, že ukládáme konzistentní event_id klíč do výstupního JSONu
                if "event_event_id" in boss and "event_id" not in boss:
                    boss["event_id"] = boss.pop("event_event_id") # Přejmenujeme klíč na standardní "event_id"
                
                processed_bosses_data.append(boss) # Přidáme bosse do nového seznamu
                if status is True:
                    defeated_boss_count += 1
                    print(f"Boss Defeated: {boss_name} (ID: {event_id_to_check}) JE PORAŽEN.")
                else:
                    print(f"Boss Not Defeated: {boss_name} (ID: {event_id_to_check}) NENÍ poražen.")
            # else: Boss, u kterého selhalo mapování (status is None), je přeskočen a nebude v processed_bosses_data

        print(f"\nPočet bossů, pro které byl status úspěšně zjištěn: {len(processed_bosses_data)}")
        print(f"Celkový počet detekovaných poražených bossů (z těch výše): {defeated_boss_count}")

        if processed_bosses_data:
            output_filename = "data/Bosses/reference_boss_ids_processed.json" # TODO: Možná také jako argument
            try:
                with open(output_filename, 'w', encoding='utf-8') as f:
                    json.dump(processed_bosses_data, f, ensure_ascii=False, indent=4)
                print(f"Zpracovaný seznam bossů (pouze ti s platným mapováním) uložen do: {output_filename}")
            except IOError as e:
                print(f"Chyba při ukládání zpracovaného JSON souboru {output_filename}: {e}")
        else:
            print("Chyba: Nepodařilo se získat dešifrovaná data event flagů. Zpracování bossů neproběhlo.")

if __name__ == "__main__":
    main()